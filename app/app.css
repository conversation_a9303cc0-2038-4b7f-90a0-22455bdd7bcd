@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

/* 🔤 Google Sans Code 字体定义 */
@font-face {
    font-family: 'Google Sans Code';
    src: url('/fonts/GoogleSansCode-VariableFont_wght.ttf')
        format('truetype-variations');
    font-weight: 100 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Google Sans Code';
    src: url('/fonts/GoogleSansCode-Italic-VariableFont_wght.ttf')
        format('truetype-variations');
    font-weight: 100 900;
    font-style: italic;
    font-display: swap;
}

@theme {
    --font-sans:
        ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
        'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-mono:
        'Fira Code', 'Google Sans Code', ui-monospace, 'SFMono-Regular',
        'SF Mono', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
}

/* 🎮 莉莉丝主题颜色定义 */
@theme {
    --color-lilith-primary: oklch(0.418 0.301 27.325); /* 莉莉丝红 #D30200 */
    --color-lilith-primary-foreground: oklch(1 0 0); /* 白色文字 */
    /* 浅灰 */
    /* --color-lilith-secondary: oklch(0.92 0 0); */
    /* 深灰文字 */
    /* --color-lilith-secondary-foreground: oklch(0.145 0 0); */

    /* 黑色 */
    /* --color-lilith-accent: oklch(0.145 0 0); */
    /* 白色文字 */
    /* --color-lilith-accent-foreground: oklch(1 0 0); */

    /* 中灰 */
    /* --color-lilith-muted: oklch(0.85 0 0); */
    /* 深灰文字 */
    /* --color-lilith-muted-foreground: oklch(0.4 0 0); */

    /* 危险色 = 莉莉丝红 */
    --color-lilith-destructive: oklch(0.418 0.301 27.325);

    /* 边框灰 */
    /* --color-lilith-border: oklch(0.7 0 0); */
    /* 输入框浅灰 */
    /* --color-lilith-input: oklch(0.95 0 0); */
    /* 焦点环 = 莉莉丝红 */
    /* --color-lilith-ring: oklch(0.418 0.301 27.325); */
}

@theme inline {
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/* 🌞 默认亮色主题 */
:root {
    --radius: 0.625rem;

    /* =============== 亮色主题 =============== */
    /* 背景/前景 */
    --background: theme(colors.white);
    --foreground: theme(colors.zinc.950);

    /* 卡片/弹窗 */
    --card: theme(colors.white);
    --card-foreground: theme(colors.zinc.950);
    --popover: theme(colors.white);
    --popover-foreground: theme(colors.zinc.950);

    /* 主/次级按钮 */
    --primary: theme(colors.zinc.900);
    --primary-foreground: theme(colors.zinc.50);
    --secondary: theme(colors.zinc.100);
    --secondary-foreground: theme(colors.zinc.900);

    /* 禁音 */
    --muted: theme(colors.zinc.100);
    --muted-foreground: theme(colors.zinc.500);

    /* 强调 */
    --accent: theme(colors.zinc.100);
    --accent-foreground: theme(colors.zinc.900);

    /* 状态色 */
    /* --destructive: oklch(0.577 0.245 27.325); 保持原色，也可自行替换 */
    --destructive: var(--color-lilith-destructive); /* 保持原色，也可自行替换 */

    /* 边框/输入/焦点环 */
    --border: theme(colors.zinc.200);
    --input: theme(colors.zinc.200);
    --ring: theme(colors.zinc.400);

    /* 图表色（保持原 oklch，可不动） */
    --chart-1: oklch(0.584 0.207 261.138);
    --chart-2: oklch(0.74 0.131 264.376);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);

    /* 侧边栏 */
    --sidebar: theme(colors.zinc.50);
    --sidebar-foreground: theme(colors.zinc.950);
    --sidebar-primary: theme(colors.zinc.900);
    --sidebar-primary-foreground: theme(colors.zinc.50);
    --sidebar-accent: theme(colors.zinc.100);
    --sidebar-accent-foreground: theme(colors.zinc.900);
    --sidebar-border: theme(colors.zinc.200);
    --sidebar-ring: theme(colors.zinc.400);
}

/* =============== 暗色主题 =============== */
.dark {
    /* 背景/前景 */
    --background: theme(colors.zinc.950);
    --foreground: theme(colors.zinc.50);

    /* 卡片/弹窗 */
    --card: theme(colors.zinc.900);
    --card-foreground: theme(colors.zinc.50);
    --popover: theme(colors.zinc.900);
    --popover-foreground: theme(colors.zinc.50);

    /* 主/次级按钮 */
    --primary: theme(colors.zinc.50);
    --primary-foreground: theme(colors.zinc.900);
    --secondary: theme(colors.zinc.800);
    --secondary-foreground: theme(colors.zinc.50);

    /* 强调/禁音 */
    --muted: theme(colors.zinc.800);
    --muted-foreground: theme(colors.zinc.400);
    --accent: theme(colors.zinc.800);
    --accent-foreground: theme(colors.zinc.50);

    /* 状态色 */
    /* --destructive: oklch(0.704 0.191 22.216); */
    --destructive: var(--color-lilith-destructive);

    /* 边框/输入/焦点环（使用带透明度的 zinc） */
    --border: theme(colors.zinc.700);
    --input: theme(colors.zinc.800);
    --ring: theme(colors.zinc.500);

    /* 图表色 */
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);

    /* 侧边栏 */
    --sidebar: theme(colors.zinc.900);
    --sidebar-foreground: theme(colors.zinc.50);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: theme(colors.zinc.50);
    --sidebar-accent: theme(colors.zinc.800);
    --sidebar-accent-foreground: theme(colors.zinc.50);
    --sidebar-border: theme(colors.zinc.800);
    --sidebar-ring: theme(colors.zinc.500);
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    html {
        /* 现代浏览器的滚动条空间保留方案 - 这是关键！ */
        scrollbar-gutter: stable;
    }

    body {
        @apply bg-background text-foreground;
        /* 防止水平滚动条 */
        overflow-x: hidden;
    }
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
    width: 12px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: var(--color-lilith-muted);
    border-radius: 8px;
    border: 2px solid transparent;
    background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-lilith-border);
}

::-webkit-scrollbar-corner {
    background: transparent;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-thumb {
    background-color: var(--color-lilith-muted-foreground);
}

.dark ::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-lilith-border);
}

/* Header 相关样式 */
@layer components {
    .container {
        @apply mx-auto w-full px-4 md:px-6 lg:px-8;
    }

    /* 搜索框快捷键样式 */
    kbd {
        @apply bg-muted text-muted-foreground pointer-events-none inline-flex h-5 items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-100 select-none;
    }

    /* 渐变文字效果 */
    .gradient-text {
        @apply from-lilith-primary to-lilith-primary/80 bg-gradient-to-r bg-clip-text text-transparent;
    }

    /* 🎮 Switch 组件使用 lilith 主题色 */
    [data-slot='switch'][data-state='checked'] {
        background-color: var(--color-lilith-primary) !important;
    }

    /* Switch thumb 在启用状态下的颜色调整 */
    [data-slot='switch'][data-state='checked'] [data-slot='switch-thumb'] {
        background-color: var(--color-lilith-primary-foreground) !important;
    }
}

/* 搜索框聚焦动画 */
@keyframes search-focus {
    0% {
        box-shadow: 0 0 0 0 var(--color-ring);
    }
    100% {
        box-shadow: 0 0 0 2px var(--color-ring);
    }
}

.search-input:focus-within {
    animation: search-focus 0.2s ease-out;
}
